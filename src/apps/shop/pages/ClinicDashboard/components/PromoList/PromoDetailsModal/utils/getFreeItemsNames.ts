import React from 'react';
import { BenefitType } from '@/types/common';
import { LocalCartItem } from '../PromoDetailsModal';

export const getFreeItemsNames = ({
  cartItems,
  freeBenefit,
}: {
  cartItems: LocalCartItem[];
  freeBenefit: BenefitType;
}) => {
  if (!freeBenefit.freeProductOffer) {
    // Return JSX element with line breaks for demo items
    return (
      <div>
        <div>Product A</div>
        <div>Product B</div>
        <div>Product C</div>
      </div>
    );
  }

  // Return JSX element with each product name on a separate line
  return (
    <div>
      {cartItems.map((item, index) => (
        <div key={index}>{item.offer.name}</div>
      ))}
    </div>
  );
};
