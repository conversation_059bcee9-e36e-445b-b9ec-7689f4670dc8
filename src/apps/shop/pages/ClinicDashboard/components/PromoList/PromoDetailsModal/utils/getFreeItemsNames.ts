import { BenefitType } from '@/types/common';
import { LocalCartItem } from '../PromoDetailsModal';

export const getFreeItemsNames = ({
  cartItems,
  freeBenefit,
}: {
  cartItems: LocalCartItem[];
  freeBenefit: BenefitType;
}) => {
  if (!freeBenefit.freeProductOffer)
    // add line breaks in items
    return 'a\n b\n c';
  return cartItems.map((item) => item.offer.name).join('\n');

  return freeBenefit.freeProductOffer.name;
};
