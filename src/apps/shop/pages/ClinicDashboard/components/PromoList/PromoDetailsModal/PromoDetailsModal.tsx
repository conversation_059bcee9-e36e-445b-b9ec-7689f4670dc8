import { useState, useCallback, useEffect, useMemo } from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import styles from './PromoDetailsModal.module.css';
import { PromoTitle } from '../PromoTitle/PromoTitle';
import { PromoType } from '@/types/common';
import { OfferType } from '@/types';
import { PromoOfferItem } from '../PromoOfferItem/PromoOfferItem';
import { Subtotal } from '../Subtotal/Subtotal';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { getBenefitByType } from '@/libs/promotions/utils/getBenefitByType';
import { getFreeItemsCount } from '@/libs/promotions/utils/getFreeItemsCount';
import { getCartInfo } from './utils/getCartInfo';
import { Tooltip } from '@/libs/ui/Tooltip';
import { getFreeItemsNames } from './utils/getFreeItemsNames';

type PromoOfferModalOptions = ModalOptionProps & {
  promoType: PromoType['type'];
  title: string;
  offers: OfferType[];
  benefits: PromoType['benefits'];
  requirements: PromoType['requirements'];
};

export type LocalCartItem = {
  offerId: string;
  offer: OfferType;
  quantity: number;
  freeItemsCount: number;
  freeOfferSalePrice: number;
};

export const PromoDetailsModal = () => {
  const { addToCart, updatingProductIds } = useCartStore();
  const cartProductMapState = useCartProductMapState();
  const [localCartItems, setLocalCartItems] = useState<
    Map<string, LocalCartItem>
  >(new Map());
  const { modalOption, openModal } = useModalStore();
  const {
    benefits = [],
    requirements = [],
    offers,
    promoType,
    title,
  } = modalOption as PromoOfferModalOptions;

  const cartItems = Array.from(localCartItems.values());

  const {
    subtotalPaidItems,
    subtotalFreeItems,
    paidItemsCount,
    freeItemsCount,
  } = useMemo(() => getCartInfo(cartItems), [cartItems]);

  const freeBenefit = getBenefitByType(benefits, 'give_free_product');

  const handleAddAllToCart = useCallback(() => {
    const itemsArray = Array.from(localCartItems.values());
    if (!itemsArray || itemsArray.length === 0) return;

    const offers = itemsArray.map((item) => {
      const currentQuantityInCart =
        cartProductMapState[item.offerId]?.quantity ?? 0;
      const newTotalQuantity = currentQuantityInCart + item.quantity;

      return {
        productOfferId: item.offerId,
        quantity: newTotalQuantity,
      };
    });

    addToCart({
      offers,
      onError: (message: string) => {
        console.error('Failed to add item to cart:', message);
      },
    });

    openModal({
      name: MODAL_NAME.PROMO_MATCHER_CONGRATS,
      title,
      savings: subtotalFreeItems,
    });
  }, [
    title,
    localCartItems,
    cartProductMapState,
    openModal,
    addToCart,
    subtotalFreeItems,
  ]);

  const updateLocalCartItem = useCallback(
    (offer: OfferType, quantity: number) => {
      const freeOffer = freeBenefit.freeProductOffer ?? offer;
      const freeOfferSalePrice =
        getProductOfferComputedData(freeOffer).salePrice;

      setLocalCartItems((prev) => {
        const newItems = new Map(prev);
        newItems.set(offer.id, {
          offerId: offer.id,
          offer,
          quantity,
          freeItemsCount: getFreeItemsCount({
            requirements,
            quantity,
            freeBenefit,
          }),
          freeOfferSalePrice,
        });
        return newItems;
      });
    },
    [freeBenefit, requirements],
  );

  useEffect(() => {
    if (!offers) return;
    offers.map((offer) => {
      updateLocalCartItem(offer, offer.increments);
    });
  }, [offers, updateLocalCartItem]);

  if (!offers) return null;

  const isLoading = Array.from(localCartItems.keys()).some((offerId) =>
    updatingProductIds.has(offerId),
  );

  return (
    <Modal
      name={MODAL_NAME.PROMO_MATCHER_PRODUCTS}
      size="auto"
      withCloseButton
      customClasses={{ header: styles.modalHeader, body: styles.modalBody }}
    >
      <div className="flex flex-col">
        <div className="flex-col bg-white p-6 pt-0">
          <h3 className="mb-2 text-2xl font-medium">
            You&apos;re Almost There!
          </h3>
          <p className="mb-6 text-sm text-black/65">
            Follow the steps below to claim your savings before this offer
            expires.
          </p>
          <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
            <PromoTitle promoType={promoType} title={title} />
            <div className="divider-h"></div>
            <div className="grid gap-2">
              {offers.map((offer) => (
                <PromoOfferItem
                  key={offer.id}
                  offer={offer}
                  onQuantityChange={updateLocalCartItem}
                />
              ))}
            </div>
            <span className="mr-2 mb-4 inline-block text-sm">
              Buy now and get <strong>{freeItemsCount} for free!</strong>
            </span>
            <Tooltip
              label={getFreeItemsNames({ freeBenefit, cartItems })}
              className="max-w-full"
            >
              <span className="text-xs font-semibold text-blue-600">
                See List
              </span>
            </Tooltip>
            <div className="divider-h"></div>
            <div className="flex items-center justify-between">
              <Subtotal
                subtotal={subtotalPaidItems}
                itemsCount={paidItemsCount + freeItemsCount}
                originalPrice={subtotalFreeItems + subtotalPaidItems}
                showOriginalPrice={true}
              />
              <div className="w-52">
                <Button
                  className="w-full"
                  loading={isLoading}
                  disabled={localCartItems.size === 0}
                  onClick={handleAddAllToCart}
                >
                  Add to Cart
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};
